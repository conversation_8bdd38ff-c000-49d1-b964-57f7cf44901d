"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  CalendarIcon,
  ClockIcon,
  DollarSignIcon,
  MapPinIcon,
} from "lucide-react";
import Image from "next/image";
import jackAvatar from "@/assets/images/jack.png";
import { BidCard } from "@/components/bid/bid-card";
import { PusherChat } from "@/components/chat";
import { CrewList } from "@/components/contractor/crew-list";
import { ImageViewerDialog } from "@/components/job/image-viewer-dialog";
import { ProjectActions } from "@/components/projects/project-actions";
import { HomeownerBidFlowTour } from "@/components/tours/homeowner-bid-flow-tour";
import { useTRPC } from "@/components/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import type { Bid, Job } from "@/db/schema";
import { getStatusVariant, JOB_STATUS_VARIANTS } from "@/lib/utils";

interface ProjectDetailContentProps {
  jobId: string;
  userId: string;
  userRole: string;
}

export function ProjectDetailContent({
  jobId,
  userId,
  userRole,
}: ProjectDetailContentProps) {
  const trpc = useTRPC();

  const { data: job } = useQuery(
    trpc.jobs.getById.queryOptions({
      id: jobId,
    }),
  );

  if (!job) {
    return <div>Loading...</div>;
  }

  // Type assertion with proper Job type
  const typedJob = job as Job;

  const isHomeowner = userRole === "homeowner";
  const isQuickHire = typedJob.jobType === "QUICK_HIRE";
  const acceptedBid = typedJob.bids?.find(
    (bid: Bid) => bid.status === "ACCEPTED",
  );

  return (
    <div className="space-y-6">
      {isHomeowner &&
        typedJob.status === "PUBLISHED" &&
        typedJob.bids &&
        typedJob.bids.length > 0 && <HomeownerBidFlowTour />}

      {/* Mobile sticky footer action bar with AI chat - positioned at bottom for easy thumb access */}
      <div className="fixed right-0 bottom-0 left-0 z-50 border-t bg-background/95 p-2 backdrop-blur supports-[backdrop-filter]:bg-background/60 lg:hidden">
        <div className="mx-auto flex max-w-screen-sm items-center gap-2">
          {/* Project actions */}
          <div className="flex flex-1 gap-2">
            <ProjectActions
              job={typedJob}
              userRole={userRole}
              layout="mobile"
            />
          </div>

          {/* AI Chat button with Jack's avatar */}
          <button
            type="button"
            onClick={() => {
              // Find and click the existing PopupChat button
              const chatButton = document.querySelector(
                "[data-chat-trigger]",
              ) as HTMLButtonElement;
              if (chatButton) {
                chatButton.click();
              }
            }}
            className="h-12 w-12 flex-shrink-0 rounded-full p-0 transition-colors hover:bg-accent"
          >
            <div className="h-10 w-10 overflow-hidden rounded-full">
              <Image
                src={jackAvatar}
                alt="Jack AI Assistant"
                width={40}
                height={40}
                className="h-full w-full object-cover"
              />
            </div>
          </button>
        </div>
      </div>

      {/* Desktop layout with grid - matching reference implementation */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main content - mobile-first, full width on mobile */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Project Details</CardTitle>
                <Badge
                  variant={getStatusVariant(
                    typedJob.status,
                    JOB_STATUS_VARIANTS,
                  )}
                >
                  {typedJob.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">Property</h3>
                  <p>{typedJob.property?.name}</p>
                  <div className="flex items-center gap-2">
                    <MapPinIcon className="size-4 text-tradecrews-orange" />
                    {typedJob.property?.address && (
                      <p className="text-muted-foreground">
                        {typedJob.property.address.street},{" "}
                        {typedJob.property.address.city},{" "}
                        {typedJob.property.address.state}{" "}
                        {typedJob.property.address.zip}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex flex-wrap gap-4">
                  <div className="flex items-center gap-2">
                    <CalendarIcon className="h-4 w-4 text-tradecrews-orange" />
                    <div>
                      <p className="text-muted-foreground text-sm">
                        Start Date
                      </p>
                      <p>{format(typedJob.startsAt, "PPP")}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <ClockIcon className="h-4 w-4 text-tradecrews-orange" />
                    <div>
                      <p className="text-muted-foreground text-sm">
                        Bid Deadline
                      </p>
                      <p>{format(typedJob.deadline, "PPP")}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <DollarSignIcon className="h-4 w-4 text-tradecrews-orange" />
                    <div>
                      <p className="text-muted-foreground text-sm">Budget</p>
                      <p>${typedJob.budget}</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Project Images */}
          {typedJob.images && typedJob.images.length > 0 && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Project Images</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3">
                  {typedJob.images.map((image, index: number) => (
                    <div
                      key={image.id}
                      className="overflow-hidden rounded-md border"
                    >
                      <ImageViewerDialog
                        images={
                          typedJob.images?.map((img) => ({
                            url: img.url,
                            description: img.description,
                          })) || []
                        }
                        initialIndex={index}
                        title={`${typedJob.name} - Project Images`}
                      >
                        <div className="relative aspect-video cursor-pointer">
                          <Image
                            src={image.url}
                            alt={
                              image.description || `Project image ${index + 1}`
                            }
                            fill
                            className="object-cover"
                          />
                        </div>
                      </ImageViewerDialog>
                      {image.description && (
                        <div className="p-3">
                          <p className="text-sm">{image.description}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Tasks */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Tasks</CardTitle>
            </CardHeader>
            <CardContent>
              {typedJob.tasks?.length === 0 ? (
                <p className="text-muted-foreground">
                  No tasks added to this project.
                </p>
              ) : (
                <div className="space-y-4">
                  {typedJob.tasks?.map((task, index: number) => (
                    <div key={task.id} className="rounded-md border p-4">
                      <h4 className="font-medium">
                        Task {index + 1}: {task.name}
                      </h4>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div>
          {isQuickHire ? (
            <Card>
              <CardHeader>
                <CardTitle>Messages</CardTitle>
              </CardHeader>
              <CardContent>
                <PusherChat jobId={typedJob.id} userId={userId} />
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Bids</CardTitle>
              </CardHeader>
              <CardContent>
                {typedJob.bids && typedJob.bids.length > 0 ? (
                  <div className="space-y-4">
                    {typedJob.bids.map((bid) => (
                      <BidCard
                        key={bid.id}
                        bidId={bid.id}
                        showAcceptButton={
                          isHomeowner && typedJob.status === "PUBLISHED"
                        }
                      />
                    ))}
                  </div>
                ) : (
                  <div className="rounded-md bg-muted/50 p-4 text-center text-muted-foreground">
                    <p>No bids have been submitted for this job yet.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {acceptedBid && (
            <div className="mt-6">
              <h3 className="mb-2 font-medium">Contractor's Crew</h3>
              <CrewList organizationId={acceptedBid.organizationId} />
            </div>
          )}

          {/* Desktop action buttons - hidden on mobile */}
          <div className="mt-6 hidden lg:block">
            <ProjectActions
              job={typedJob}
              userRole={userRole}
              layout="vertical"
            />
          </div>
        </div>
      </div>

      {/* Add bottom padding to prevent content from being hidden behind shorter sticky footer on mobile */}
      <div className="h-16 lg:h-0" />
    </div>
  );
}
