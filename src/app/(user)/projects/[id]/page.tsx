import { headers } from "next/headers";
import { notFound, redirect } from "next/navigation";
import { Suspense } from "react";
import { DetailPageLayout } from "@/components/common/page-layouts";
import { ProjectDetailContent } from "@/components/projects/project-detail-content";
import { getQueryClient, HydrateClient, trpc } from "@/components/trpc/server";
import { Skeleton } from "@/components/ui/skeleton";
import { auth } from "@/lib/auth";
import { getJobStatusVariant } from "@/lib/utils";

function ProjectDetailSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="space-y-6 lg:col-span-2">
          <Skeleton className="h-[400px] w-full" />
          <Skeleton className="h-[200px] w-full" />
          <Skeleton className="h-[300px] w-full" />
        </div>
        <div className="space-y-6">
          <Skeleton className="h-[500px] w-full" />
        </div>
      </div>
    </div>
  );
}

type Props = {
  params: Promise<{ id: string }>;
};

export default async function JobDetailPage({ params }: Props) {
  const { id } = await params;
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user) {
    redirect("/sign-in");
  }

  const queryClient = getQueryClient();

  // Fetch the job data
  const job = await queryClient.fetchQuery(
    trpc.jobs.getById.queryOptions({
      id,
    }),
  );

  if (!job) {
    notFound();
  }

  // Prefetch related data
  await Promise.all([
    // Prefetch bids if it's a standard job
    job.jobType !== "QUICK_HIRE" && job.bids?.length
      ? Promise.all(
          job.bids.map((bid) =>
            queryClient.prefetchQuery(
              trpc.bids.getById.queryOptions({ id: bid.id }),
            ),
          ),
        )
      : Promise.resolve(),

    // Prefetch crew data if there's an accepted bid
    (() => {
      const acceptedBid = job.bids?.find((bid) => bid.status === "ACCEPTED");
      return acceptedBid
        ? queryClient.prefetchQuery(
            trpc.contractor.getCrewMembers.queryOptions({
              organizationId: acceptedBid.organizationId,
            }),
          )
        : Promise.resolve();
    })(),
  ]);

  return (
    <DetailPageLayout
      header={{
        title: job.name,
        description: `Project ID: ${job.id}`,
        badge: {
          label: job.status,
          variant: (() => {
            const statusVariant = getJobStatusVariant(job.status);
            // Map status variants to badge-compatible variants
            if (statusVariant === "success") {
              return "default";
            }
            return statusVariant as
              | "default"
              | "secondary"
              | "destructive"
              | "outline";
          })(),
        },
        backButton: {
          href: "/projects",
          label: "Back to Projects",
        },
      }}
    >
      <HydrateClient>
        <Suspense fallback={<ProjectDetailSkeleton />}>
          <ProjectDetailContent
            jobId={id}
            userId={session.user.id}
            userRole={session.user.role}
          />
        </Suspense>
      </HydrateClient>
    </DetailPageLayout>
  );
}
